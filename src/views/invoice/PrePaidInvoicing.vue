<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  getPrePaidInvoiceList,
  submitPrePaidInvoice,
  getAccountSeqDetail,
  InvoiceSimpleInfo,
} from "../../services/invoice";
import { getCustomersSimpleList } from "../../services/customer";
import { getInvocieInfoSimpleList } from "../../services/invoice";
import { optionLoaders } from "../../utils/options";
import { formatDateToYYYYMM } from "../../utils/dateFormatter";
import type {
  PrePaidInvoiceItem,
  PrePaidInvoiceSearchParams,
  PrePaidInvoiceRequest,
  PrePaidInvoiceApiRequest,
  AccountSeqDetailInfo,
} from "../../types/invoice";
import type { CustomerSimpleInfo } from "../../types/customer";
import { useToast } from "primevue/usetoast";

const toast = useToast();
const loading = ref(false);
const items = ref<PrePaidInvoiceItem[]>([]);
const totalRecords = ref(0);

// 筛选表单
const searchForm = ref<PrePaidInvoiceSearchParams>({
  account_seq: "",
  customer_num: "",
  total_num: "",
  page: 1,
  pageSize: 20,
});

// 选中的项目
const selectedItems = ref<PrePaidInvoiceItem[]>([]);

// 客户选项
const customerOptions = ref<CustomerSimpleInfo[]>([]);

// 预开票抽屉相关状态
const drawerVisible = ref(false);
const drawerLoading = ref(false);

// 发票信息选项
const invoiceInfoOptions = ref<InvoiceSimpleInfo[]>([]);

// 分账序号详细信息
const accountSeqDetail = ref<AccountSeqDetailInfo | null>(null);

// 表单验证错误状态
const fieldErrors = ref<Record<string, string>>({});

// 预开票表单数据
const invoiceForm = ref<PrePaidInvoiceRequest>({
  orders: [],
  amount: "0",
  account_seq: "",
  invoice_info_id: 0,
  invoice_type: "",
  tax_rate: 0,
  tax_amount: "0",
  currency_type: "",
  invoice_currency_type: "",
  exchange_rate: "",
  signing_entity: "",
  customer_num: "",
  remark: "",
});

// 计算是否可以进行预开票（选中项目的分账序号必须一致）
const canPreInvoice = computed(() => {
  if (selectedItems.value.length === 0) return false;

  const firstAccountSeq = selectedItems.value[0].account_seq;
  return selectedItems.value.every(
    (item) => item.account_seq === firstAccountSeq
  );
});

// 获取数据
const loadPrePaidInvoice = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getPrePaidInvoiceList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadPrePaidInvoice();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    customer_num: "",
    total_num: "",
    page: 1,
    pageSize: 20,
  };
  loadPrePaidInvoice();
};

// 分页变化
const onPageChange = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadPrePaidInvoice();
};

// 加载客户列表
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    if (response.code === 200) {
      customerOptions.value = response.data;
    }
  } catch (error) {
    console.error("加载客户列表失败:", error);
  }
};

// 加载发票信息选项
const loadInvoiceInfoOptions = async (account_seq: string) => {
  try {
    const response = await getInvocieInfoSimpleList(account_seq);
    if (response.code === 200) {
      invoiceInfoOptions.value = response.data;

      // 默认选择第一条发票信息
      if (response.data && response.data.length > 0) {
        const firstInvoiceInfo = response.data[0];
        invoiceForm.value.invoice_info_id = firstInvoiceInfo.id;
        invoiceForm.value.invoice_type = firstInvoiceInfo.customer_invoice_type;
      } else {
        // 如果没有发票信息，清空相关字段
        invoiceForm.value.invoice_info_id = 0;
        invoiceForm.value.invoice_type = "";
        toast.add({
          severity: "warn",
          summary: "提示",
          detail: "该分账序号暂无可用的发票信息",
          life: 3000,
        });
      }
    }
  } catch (error) {
    console.error("加载发票信息选项失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票信息失败",
      life: 3000,
    });
  }
};

// 获取分账序号详细信息
const loadAccountSeqDetail = async (account_seq: string) => {
  try {
    const response = await getAccountSeqDetail(account_seq);
    if (response.code === 200) {
      accountSeqDetail.value = response.data;
      // 自动填充相关字段，包括税率为0的情况
      invoiceForm.value.tax_rate = response.data.tax || 0;
      invoiceForm.value.customer_num = response.data.customer_num;
      // 重新计算税额
      calculateTaxAmount();
    }
  } catch (error) {
    console.error("获取分账序号详细信息失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取分账序号详细信息失败",
      life: 3000,
    });
  }
};

// 发票信息选择变化处理
const onInvoiceInfoChange = (invoiceInfoId: number) => {
  const selectedInvoiceInfo = invoiceInfoOptions.value.find(
    (info) => info.id === invoiceInfoId
  );
  if (selectedInvoiceInfo) {
    invoiceForm.value.invoice_type = selectedInvoiceInfo.customer_invoice_type;
  }
};

// 错误处理回调函数
const handleOptionsError = (_error: any, message: string) => {
  toast.add({
    severity: "error",
    summary: "错误",
    detail: message,
    life: 3000,
  });
};

// 签约主体选项
const signingEntityOptions = ref<{ label: string; value: string }[]>([]);
const loadSigningEntityOptions = () =>
  optionLoaders.signContractEntity(signingEntityOptions, handleOptionsError);

// 货币类型选项
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const loadCurrencyOptions = () =>
  optionLoaders.currencyType(currencyOptions, handleOptionsError);

// 打开预开票抽屉
const openInvoiceDrawer = () => {
  if (selectedItems.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择要预开票的记录",
      life: 3000,
    });
    return;
  }

  if (!canPreInvoice.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "选中的记录分账序号不一致，无法进行预开票",
      life: 3000,
    });
    return;
  }

  clearFieldErrors(); // 清空表单错误

  // 初始化表单数据
  const firstItem = selectedItems.value[0];
  invoiceForm.value = {
    orders: selectedItems.value.map((item, index) => ({
      order_id: selectedItems.value[index].id,
      order_num: item.order_num,
      order_info: [
        {
          amount: "0",
          invoice_month: new Date(),
        },
      ],
    })),
    amount: "0",
    account_seq: firstItem.account_seq,
    invoice_info_id: 0,
    invoice_type: "",
    tax_rate: 0,
    tax_amount: "0",
    currency_type: firstItem.currency_type,
    invoice_currency_type: "",
    exchange_rate: "",
    signing_entity: "",
    customer_num: "",
    remark: "",
  };

  // 计算总金额
  calculateTotalAmount();

  drawerVisible.value = true;

  // 加载选项数据和分账序号详细信息
  loadInvoiceInfoOptions(firstItem.account_seq);
  loadAccountSeqDetail(firstItem.account_seq);
  loadSigningEntityOptions();
  loadCurrencyOptions();
};

// 关闭预开票抽屉
const closeInvoiceDrawer = () => {
  drawerVisible.value = false;
  accountSeqDetail.value = null;
  clearFieldErrors(); // 清空表单错误
  invoiceForm.value = {
    orders: [],
    amount: "0",
    account_seq: "",
    invoice_info_id: 0,
    invoice_type: "",
    tax_rate: 0,
    tax_amount: "0",
    currency_type: "",
    invoice_currency_type: "",
    exchange_rate: "",
    signing_entity: "",
    customer_num: "",
    remark: "",
  };
};

// 计算总金额
const calculateTotalAmount = () => {
  const total = invoiceForm.value.orders.reduce((sum, order) => {
    const orderTotal = order.order_info.reduce((orderSum, info) => {
      return orderSum + parseFloat(info.amount || "0");
    }, 0);
    return sum + orderTotal;
  }, 0);
  invoiceForm.value.amount = total.toFixed(2);
  // 重新计算税额
  calculateTaxAmount();
};

// 计算税额
const calculateTaxAmount = () => {
  const amount = parseFloat(invoiceForm.value.amount || "0");
  const taxRate = invoiceForm.value.tax_rate || 0;
  if (amount > 0 && taxRate > 0) {
    // 税额 = 金额 - (金额 / (1 + 税率/100))
    const taxAmount = amount - amount / (1 + taxRate / 100);
    invoiceForm.value.tax_amount = taxAmount.toFixed(2);
  } else {
    invoiceForm.value.tax_amount = "0";
  }
};

// 添加订单信息项
const addOrderInfoItem = (orderIndex: number) => {
  invoiceForm.value.orders[orderIndex].order_info.push({
    amount: "0",
    invoice_month: new Date(), // 使用当前日期
  });
};

// 删除订单信息项
const removeOrderInfoItem = (orderIndex: number, infoIndex: number) => {
  const orderInfo = invoiceForm.value.orders[orderIndex].order_info;
  if (orderInfo.length > 1) {
    orderInfo.splice(infoIndex, 1);
    calculateTotalAmount();
  }
};

// 清空表单错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
};

// 表单验证
const validateInvoiceForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "invoice_info_id", label: "发票信息" },
    { key: "signing_entity", label: "签约主体" },
    { key: "invoice_currency_type", label: "开票币种" },
    { key: "exchange_rate", label: "汇率" },
  ];
  console.log(invoiceForm.value);
  
  requiredFields.forEach((field) => {
    const value = invoiceForm.value[field.key as keyof PrePaidInvoiceRequest];
    console.log(field.key, value);
    if (
      value === null ||
      value === undefined ||
      value === 0 ||
      (typeof value === "string" && value.trim() === "")
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  // 验证汇率必须大于0
  if (
    invoiceForm.value.exchange_rate &&
    parseFloat(invoiceForm.value.exchange_rate) <= 0
  ) {
    fieldErrors.value.exchange_rate = "汇率必须大于0";
    isValid = false;
  }

  // 验证订单信息
  for (let i = 0; i < invoiceForm.value.orders.length; i++) {
    const order = invoiceForm.value.orders[i];
    if (!order.order_id) {
      fieldErrors.value[`order_${i}_id`] = `订单 ${i + 1} 的订单ID不能为空`;
      isValid = false;
    }

    for (let j = 0; j < order.order_info.length; j++) {
      const info = order.order_info[j];
      const amountKey = `order_${i}_info_${j}_amount`;
      const monthKey = `order_${i}_info_${j}_month`;

      if (!info.amount || parseFloat(info.amount) <= 0) {
        fieldErrors.value[amountKey] = "金额必须大于0";
        isValid = false;
      }
      if (!info.invoice_month) {
        fieldErrors.value[monthKey] = "账期不能为空";
        isValid = false;
      }
    }
  }

  // 如果有错误，显示总体提示
  if (!isValid) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
  }

  return isValid;
};

// 提交预开票
const submitInvoice = async () => {
  // 表单验证
  if (!validateInvoiceForm()) {
    return;
  }

  drawerLoading.value = true;
  try {
    // 创建一个新的请求对象，格式化日期
    const formattedRequest: PrePaidInvoiceApiRequest = {
      amount: invoiceForm.value.amount,
      account_seq: invoiceForm.value.account_seq,
      invoice_info_id: invoiceForm.value.invoice_info_id,
      invoice_type: invoiceForm.value.invoice_type,
      tax_rate: invoiceForm.value.tax_rate,
      tax_amount: invoiceForm.value.tax_amount,
      currency_type: invoiceForm.value.currency_type,
      invoice_currency_type: invoiceForm.value.invoice_currency_type,
      exchange_rate: invoiceForm.value.exchange_rate,
      signing_entity: invoiceForm.value.signing_entity,
      customer_num: invoiceForm.value.customer_num,
      remark: invoiceForm.value.remark,
      orders: invoiceForm.value.orders.map((order) => ({
        order_id: order.order_id,
        order_num: order.order_num,
        order_info: order.order_info.map((info) => ({
          amount: info.amount,
          invoice_month: formatDateToYYYYMM(info.invoice_month as Date),
        })),
      })),
    };

    const response = await submitPrePaidInvoice(formattedRequest);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "预开票提交成功",
        life: 3000,
      });
      closeInvoiceDrawer();
      selectedItems.value = [];
      loadPrePaidInvoice();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "预开票提交失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("预开票提交失败:", error);
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "预开票提交失败",
        life: 3000,
      });
    }
  } finally {
    drawerLoading.value = false;
  }
};

onMounted(() => {
  loadPrePaidInvoice();
  loadCustomerOptions();
});
</script>

<template>
  <!-- 搜索工具栏 -->
  <Toolbar class="mb-2">
    <template #start>
      <div class="flex items-center">
        <Message severity="info">待开票-预付费</Message>
      </div>
    </template>
    <template #end>
      <div class="flex flex-wrap gap-2 items-center">
        <Select
          v-model="searchForm.customer_num"
          :options="customerOptions"
          optionLabel="customer_name"
          optionValue="customer_num"
          class="w-48"
          showClear
          filter
          placeholder="选择客户"
        />
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">分账序号</label>
          <InputText v-model="searchForm.account_seq" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">订单编号</label>
          <InputText v-model="searchForm.total_num" class="w-48" />
        </FloatLabel>
        <Button @click="handleSearch" icon="pi pi-search" rounded />
        <Button
          @click="handleReset"
          icon="pi pi-refresh"
          class="p-button-secondary"
          rounded
        />
      </div>
      <Divider layout="vertical" />
      <Button
        icon="pi pi-file"
        label="预开票"
        @click="openInvoiceDrawer"
        :disabled="!canPreInvoice"
        severity="success"
      />
    </template>
  </Toolbar>

  <!-- 数据表格 -->
  <DataTable
    :value="items"
    v-model:selection="selectedItems"
    :loading="loading"
    :paginator="true"
    :rows="20"
    :totalRecords="totalRecords"
    :lazy="true"
    @page="onPageChange"
    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
    :rowsPerPageOptions="[10, 20, 50]"
    currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
    class="p-datatable-sm"
    showGridlines
    scrollable
    scrollHeight="calc(100vh - 26rem)"
    dataKey="order_num"
  >
    <template #empty>
      <div class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无预付费开票数据</p>
      </div>
    </template>

    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

    <Column field="customer_name" header="客户名称" style="min-width: 15rem">
      <template #body="{ data }">
        <span class="font-medium">{{ data.customer_name }}</span>
      </template>
    </Column>

    <Column field="order_num" header="订单编号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.order_num }}</span>
      </template>
    </Column>

    <Column field="total_num" header="合成编号" style="min-width: 15rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.total_num }}</span>
      </template>
    </Column>

    <Column field="income_type" header="收入类型" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.income_type" />
      </template>
    </Column>

    <Column field="account_seq" header="分账序号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.account_seq }}</span>
      </template>
    </Column>

    <Column field="bill_status" header="计费状态" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag
          :value="data.bill_status"
          :class="
            data.bill_status === '计费中' ? 'p-tag-success' : 'p-tag-secondary'
          "
        />
      </template>
    </Column>

    <Column field="pay_type" header="付费类型" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag :value="data.pay_type" class="p-tag-warn" />
      </template>
    </Column>
  </DataTable>

  <!-- 预开票抽屉 -->
  <Drawer
    v-model:visible="drawerVisible"
    header="预开票"
    position="right"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :style="{ width: '80rem' }"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-4">
            <div class="field">
              <label class="required">分账序号</label>
              <InputText v-model="invoiceForm.account_seq" disabled />
            </div>
            <div class="field">
              <label class="required">客户信息</label>
              <InputText
                :value="
                  accountSeqDetail
                    ? `${accountSeqDetail.customer_name}(${accountSeqDetail.customer_num})`
                    : ''
                "
                disabled
              />
            </div>
            <div class="field">
              <label class="required">汇率</label>
              <InputNumber
                :model-value="parseFloat(invoiceForm.exchange_rate || '0')"
                @update:model-value="(value) => { invoiceForm.exchange_rate = value?.toString() || '0'; }"
                :min="0"
                :minFractionDigits="0"
                :maxFractionDigits="10"
                placeholder="输入汇率"
                :class="{ 'p-invalid': fieldErrors.exchange_rate }"
              />
              <small v-if="fieldErrors.exchange_rate" class="p-error">
                {{ fieldErrors.exchange_rate }}
              </small>
            </div>
            <div class="field">
              <label class="required">总金额</label>
              <InputText v-model="invoiceForm.amount" disabled />
            </div>
            <div class="field">
              <label class="required">税率</label>
              <InputText
                :value="
                  invoiceForm.tax_rate !== undefined &&
                  invoiceForm.tax_rate !== null
                    ? `${invoiceForm.tax_rate}%`
                    : '0%'
                "
                disabled
              />
            </div>
            <div class="field">
              <label class="required">税额</label>
              <InputText v-model="invoiceForm.tax_amount" disabled />
            </div>
            <div class="field">
              <label class="required">发票信息</label>
              <Select
                v-model="invoiceForm.invoice_info_id"
                :options="invoiceInfoOptions"
                optionLabel="customer_invoice_name"
                optionValue="id"
                showClear
                filter
                placeholder="选择发票信息"
                :class="{ 'p-invalid': fieldErrors.invoice_info_id }"
                @change="onInvoiceInfoChange($event.value)"
              />
              <small v-if="fieldErrors.invoice_info_id" class="p-error">
                {{ fieldErrors.invoice_info_id }}
              </small>
            </div>
            <div class="field">
              <label class="required">发票类型</label>
              <InputText v-model="invoiceForm.invoice_type" disabled />
            </div>
            <div class="field">
              <label class="required">币种类型</label>
              <InputText
                v-model="invoiceForm.currency_type"
                placeholder="输入币种类型"
                disabled
              />
            </div>
            <div class="field">
              <label class="required">开票币种</label>
              <Select
                v-model="invoiceForm.invoice_currency_type"
                :options="currencyOptions"
                optionLabel="label"
                optionValue="value"
                showClear
                filter
                placeholder="选择开票币种"
                :class="{ 'p-invalid': fieldErrors.invoice_currency_type }"
              />
              <small v-if="fieldErrors.invoice_currency_type" class="p-error">
                {{ fieldErrors.invoice_currency_type }}
              </small>
            </div>
            <div class="field">
              <label class="required">签约主体</label>
              <Select
                v-model="invoiceForm.signing_entity"
                :options="signingEntityOptions"
                optionLabel="label"
                optionValue="value"
                showClear
                placeholder="选择签约主体"
                :class="{ 'p-invalid': fieldErrors.signing_entity }"
              />
              <small v-if="fieldErrors.signing_entity" class="p-error">
                {{ fieldErrors.signing_entity }}
              </small>
            </div>
            <div class="field col-span-3">
              <label>备注</label>
              <Textarea
                v-model="invoiceForm.remark"
                rows="3"
                placeholder="输入备注信息"
              />
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 订单明细 -->
      <div class="form-section">
        <h3 class="section-title">订单开票信息</h3>
        <Accordion :multiple="true" class="order-accordion">
          <AccordionPanel
            v-for="(order, orderIndex) in invoiceForm.orders"
            :key="orderIndex"
            :value="orderIndex"
          >
            <AccordionHeader>
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center gap-2">
                  <span>{{ order.order_num || `订单 ${orderIndex + 1}` }}</span>
                  <Badge
                    :value="order.order_info.length"
                    severity="info"
                    class="ml-2"
                  />
                </div>
              </div>
            </AccordionHeader>
            <AccordionContent>
              <div class="order-content">
                <div class="order-info-items">
                  <div class="flex justify-center items-center">
                    <Button
                      icon="pi pi-plus"
                      label="添加订单开票信息"
                      @click="addOrderInfoItem(orderIndex)"
                      severity="info"
                      size="small"
                      outlined
                      class="mb-2"
                    />
                  </div>
                  <div
                    v-for="(info, infoIndex) in order.order_info"
                    :key="infoIndex"
                    class="order-info-item"
                  >
                    <Fluid>
                      <div class="flex items-end gap-3">
                        <div class="field flex-1">
                          <label class="required">金额</label>
                          <InputNumber
                            :model-value="parseFloat(info.amount || '0')"
                            @update:model-value="
                              (value) => {
                                info.amount = value?.toString() || '0';
                                calculateTotalAmount();
                              }
                            "
                            placeholder="输入金额"
                            showButtons
                            :step="0.01"
                            :min="0"
                            :maxFractionDigits="2"
                            :minFractionDigits="2"
                            mode="decimal"
                            locale="zh-CN"
                            :class="{
                              'p-invalid':
                                fieldErrors[
                                  `order_${orderIndex}_info_${infoIndex}_amount`
                                ],
                            }"
                          />
                        </div>
                        <div class="field flex-1">
                          <label class="required">账期</label>
                          <DatePicker
                            v-model="info.invoice_month"
                            view="month"
                            dateFormat="yy-mm"
                            placeholder="选择账期"
                            showIcon
                            class="w-full"
                            :class="{
                              'p-invalid':
                                fieldErrors[
                                  `order_${orderIndex}_info_${infoIndex}_month`
                                ],
                            }"
                          />
                        </div>
                          <Button
                            v-if="order.order_info.length > 1"
                            icon="pi pi-trash"
                            @click="removeOrderInfoItem(orderIndex, infoIndex)"
                            severity="danger"
                            rounded
                            class="mb-4.5 mr-2"
                          />
                      </div>
                    </Fluid>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionPanel>
        </Accordion>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          @click="closeInvoiceDrawer"
          class="p-button-secondary"
        />
        <Button
          label="提交预开票"
          icon="pi pi-check"
          @click="submitInvoice"
          :loading="drawerLoading"
          class="p-button-success"
        />
      </div>
    </template>
  </Drawer>

  <Toast />
</template>

<style scoped>
/* 工具栏和表格样式 */
:deep(.p-toolbar) {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f1f5f9;
  color: #475569;
  font-weight: 600;
  border-color: #e2e8f0;
}

:deep(.p-datatable .p-datatable-tbody > tr:nth-child(odd)) {
  background: #f8fafc;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  background: #e0f2fe;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

/* 订单明细样式 */
.order-accordion {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

:deep(.order-accordion .p-accordionpanel) {
  border-bottom: 1px solid #e2e8f0;
}

:deep(.order-accordion .p-accordionpanel:last-child) {
  border-bottom: none;
}

:deep(.order-accordion .p-accordionheader) {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.order-accordion .p-accordioncontent) {
  padding: 0.5rem;
}

.order-content {
  background: #ffffff;
}

.order-info-items {
  display: flex;
  flex-direction: column;
}

.order-info-item {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
}
</style>
